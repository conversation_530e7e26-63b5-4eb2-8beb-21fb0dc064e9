// Debug script to check transform resume functionality
console.log('Debug script loaded');

// Check if transform resume button exists
const transformBtn = document.getElementById('transform-resume-btn');
console.log('Transform resume button:', transformBtn);

// Check if transformed resume tab exists
const transformedTab = document.querySelector('[data-tab="transformed-resume"]');
console.log('Transformed resume tab:', transformedTab);

// Add event listener to transform button if it exists
if (transformBtn) {
    console.log('Adding click event listener to transform button');
    transformBtn.addEventListener('click', function() {
        console.log('Transform button clicked');
        
        // Check if job description is entered
        const jobDescription = document.getElementById('job-desc-input').value;
        console.log('Job description:', jobDescription ? 'Present' : 'Missing');
        
        // Try to switch to transformed resume tab
        if (transformedTab) {
            console.log('Clicking transformed resume tab');
            transformedTab.click();
        } else {
            console.log('Transformed resume tab not found');
        }
    });
} else {
    console.log('Transform resume button not found');
}

// Check if the tab switching functionality works
const tabBtns = document.querySelectorAll('.tab-btn');
console.log('Tab buttons:', tabBtns.length);
tabBtns.forEach((btn, index) => {
    console.log(`Tab ${index}:`, btn.getAttribute('data-tab'));
});
