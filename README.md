# Resume & Email Generator

This application helps you customize your resume and generate professional emails based on job descriptions. It automatically extracts company information, identifies key skills, transforms your resume to match job requirements, and sends emails with your resume attached.

## Features

- **Job Description Analysis**: Extract company name, email, and key skills from job descriptions
- **Resume Transformation**: Automatically customize your resume to match job requirements
- **Email Generation**: Create professional email templates with your resume attached
- **PDF Generation**: Generate and download your transformed resume as a PDF
- **Automatic Email Sending**: Send emails with your resume attached directly from the application

## How to Use

### Setup

1. Clone this repository
2. Start the server:
   ```
   python server.py
   ```
3. Open your browser and navigate to `http://localhost:8000`

### Workflow

1. **Paste Job Description**:
   - Paste the job description in the first tab
   - Click "Analyze & Customize"
   - The application will automatically extract company information and key skills

2. **Resume Transformation**:
   - The application will automatically transform your resume to match the job description
   - Review the AI-suggested improvements
   - The transformed resume will be formatted to fit on one page

3. **Email Generation**:
   - The application will automatically generate an email template with the company name and relevant skills
   - The email will mention that your resume is attached

4. **Send Email**:
   - Click "Open in Email Client" to send the email
   - Your transformed resume will be automatically attached to the email

## Technical Details

### Server-Side Components

- `server.py`: Handles email sending with attachments
- Uses Python's built-in HTTP server and email libraries

### Client-Side Components

- `index.html`: Main HTML structure
- `styles.css`: Styling for the application
- `script_fixed.js`: Main JavaScript functionality
  - Job description analysis
  - Resume transformation
  - Email generation
  - PDF generation and attachment

## Customization

You can customize the application by:

1. Editing the resume templates in the `generateTransformedResume` function
2. Modifying the email templates in the `generateEmail` function
3. Adjusting the PDF generation settings in the `downloadResumePdf` function

## Notes

- The application uses client-side PDF generation with jsPDF and html2canvas
- For production use, you should configure the email sending functionality with your SMTP server details
- The application is designed to work with both technical and construction job descriptions

## License

This project is licensed under the MIT License - see the LICENSE file for details.
