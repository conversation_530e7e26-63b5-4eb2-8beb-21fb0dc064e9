// Function to force email generation
function forceGenerateEmail() {
    console.log("Force generating email");
    const jobDescription = document.getElementById('job-desc-input').value;

    if (jobDescription) {
        console.log("Job description found, extracting company info");
        const extractedInfo = extractCompanyInfo(jobDescription);
        const companyName = extractedInfo.companyName || "the Company";
        const recruiterEmail = extractedInfo.recruiterEmail || "";

        console.log("Generating email with company:", companyName);
        generateEmail(companyName, recruiterEmail);

        // Make sure the email body is visible
        const emailBody = document.getElementById('email-body');
        if (emailBody) {
            console.log("Email body element found");

            // Try to ensure the content is visible
            setTimeout(() => {
                console.log("Checking email body content after delay");
                if (!emailBody.innerHTML || emailBody.innerHTML.trim() === '') {
                    console.log("Email body still empty, trying again");
                    generateEmail(companyName, recruiterEmail);
                }
            }, 500);
        } else {
            console.log("Email body element not found");
        }
    } else {
        console.log("No job description found");
    }
}

document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM fully loaded");

    // Force generate email on page load
    setTimeout(forceGenerateEmail, 500);

    // Tab switching functionality
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    console.log("Tab buttons found:", tabBtns.length);
    console.log("Tab contents found:", tabContents.length);

    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const tabId = btn.getAttribute('data-tab');
            console.log("Tab clicked:", tabId);

            // Remove active class from all buttons and contents
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));

            // Add active class to clicked button and corresponding content
            btn.classList.add('active');
            document.getElementById(tabId).classList.add('active');

            // If switching to email template tab, ensure email is generated
            if (tabId === 'email-template') {
                console.log("Email tab clicked, ensuring email is generated");

                // Direct call to generate email
                const jobDescription = document.getElementById('job-desc-input').value;
                if (jobDescription) {
                    const extractedInfo = extractCompanyInfo(jobDescription);
                    const companyName = extractedInfo.companyName;
                    const recruiterEmail = extractedInfo.recruiterEmail;

                    if (companyName) {
                        console.log("Directly generating email from tab click");
                        generateEmail(companyName, recruiterEmail);
                    }
                }

                // Also call the ensure function as a backup
                ensureEmailGenerated();
            }
        });
    });

    // Analyze button functionality
    const analyzeBtn = document.getElementById('analyze-btn');
    analyzeBtn.addEventListener('click', analyzeJobDescription);

    // Go to email button functionality
    const goToEmailBtn = document.getElementById('go-to-email');
    goToEmailBtn.addEventListener('click', () => {
        // First generate the email directly
        const jobDescription = document.getElementById('job-desc-input').value;
        if (jobDescription) {
            const extractedInfo = extractCompanyInfo(jobDescription);
            const companyName = extractedInfo.companyName;
            const recruiterEmail = extractedInfo.recruiterEmail;

            if (companyName) {
                generateEmail(companyName, recruiterEmail);
            }
        }

        // Then switch to the email tab
        document.querySelector('[data-tab="email-template"]').click();
    });

    // Send email button functionality
    const sendEmailBtn = document.getElementById('send-email');
    sendEmailBtn.addEventListener('click', sendEmail);

    // Settings button functionality
    const settingsBtn = document.getElementById('settings-btn');
    const settingsModal = document.getElementById('settings-modal');
    const closeBtn = document.querySelector('.close');
    const saveSettingsBtn = document.getElementById('save-settings');

    // Load saved settings
    loadUserSettings();

    // Check if there's a job description and generate email on page load
    const jobDescription = document.getElementById('job-desc-input').value;
    if (jobDescription) {
        console.log("Found job description on page load, generating email");
        const extractedInfo = extractCompanyInfo(jobDescription);
        const companyName = extractedInfo.companyName;
        const recruiterEmail = extractedInfo.recruiterEmail;

        if (companyName) {
            generateEmail(companyName, recruiterEmail);
        }
    }

    // Open settings modal
    settingsBtn.addEventListener('click', () => {
        settingsModal.style.display = 'block';
    });

    // Close settings modal
    closeBtn.addEventListener('click', () => {
        settingsModal.style.display = 'none';
    });

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === settingsModal) {
            settingsModal.style.display = 'none';
        }
    });

    // Save settings
    saveSettingsBtn.addEventListener('click', saveUserSettings);

    // Transform resume button functionality
    const transformResumeBtn = document.getElementById('transform-resume-btn');
    console.log("Transform resume button:", transformResumeBtn);
    if (transformResumeBtn) {
        transformResumeBtn.addEventListener('click', transformResume);
        console.log("Added event listener to transform resume button");
    }

    // Download resume PDF button functionality
    const downloadResumePdfBtn = document.getElementById('download-resume-pdf');
    if (downloadResumePdfBtn) {
        downloadResumePdfBtn.addEventListener('click', downloadResumePdf);
    }

    // Apply to email button functionality
    const applyToEmailBtn = document.getElementById('apply-to-email');
    if (applyToEmailBtn) {
        applyToEmailBtn.addEventListener('click', applyToEmail);
    }
});

// Function to analyze job description and customize resume
function analyzeJobDescription() {
    const jobDescription = document.getElementById('job-desc-input').value;

    if (!jobDescription) {
        alert('Please paste a job description first.');
        return;
    }

    // Log the job description for debugging
    console.log("Job description:", jobDescription);

    // Extract company name and email from job description
    const extractedInfo = extractCompanyInfo(jobDescription);
    const companyName = extractedInfo.companyName;
    const recruiterEmail = extractedInfo.recruiterEmail;

    if (!companyName) {
        alert('Could not extract company name from the job description. Please make sure the company name is mentioned.');
        return;
    }

    // Show loading state
    document.getElementById('analyze-btn').textContent = 'Analyzing...';
    document.getElementById('analyze-btn').disabled = true;

    // Simulate AI analysis (in a real app, this would call an AI API)
    setTimeout(() => {
        // Generate suggestions based on job description
        const suggestions = generateSuggestions(jobDescription, companyName);

        // Update suggestions in the UI
        document.getElementById('suggestions-content').innerHTML = suggestions;

        // Generate email if recruiter email is provided
        if (recruiterEmail) {
            generateEmail(companyName, recruiterEmail);
        }

        // Customize resume based on job description
        customizeResume(jobDescription);

        // Reset button state
        document.getElementById('analyze-btn').textContent = 'Analyze & Customize';
        document.getElementById('analyze-btn').disabled = false;

        // Show AI suggestions but hide Go to Email button (since we're auto-navigating)
        document.getElementById('ai-suggestions').style.display = 'block';
        document.getElementById('go-to-email').style.display = 'none';

        // Automatically transform the resume
        setTimeout(() => {
            // First transform the resume
            console.log("Auto-transforming resume");
            transformResume();

            // Then make sure the email is generated and switch to the email tab
            setTimeout(() => {
                if (companyName) {
                    console.log("Auto-generating email before tab switch");
                    generateEmail(companyName, recruiterEmail);
                }

                // Then switch to the email tab
                document.querySelector('[data-tab="email-template"]').click();
            }, 3500); // Wait a bit longer to allow resume transformation to complete
        }, 500);
    }, 2000);
}

// Function to generate suggestions based on job description
function generateSuggestions(jobDescription, companyName) {
    // Extract keywords from job description
    const keywords = extractKeywords(jobDescription);

    // Extract company info again to show in suggestions
    const extractedInfo = extractCompanyInfo(jobDescription);

    // Generate HTML for suggestions
    let suggestionsHTML = '<ul>';

    suggestionsHTML += `<li><strong>Detected Company:</strong> ${companyName}</li>`;
    if (extractedInfo.recruiterEmail) {
        suggestionsHTML += `<li><strong>Detected Email:</strong> ${extractedInfo.recruiterEmail}</li>`;
    }

    // Key technologies and tools section
    suggestionsHTML += '<li><strong>Key technologies to highlight:</strong> ';
    suggestionsHTML += keywords.slice(0, 5).join(', ');
    suggestionsHTML += '</li>';

    // Additional technologies that might be relevant
    const additionalTech = getAdditionalTechnologies(keywords, jobDescription);
    if (additionalTech.length > 0) {
        suggestionsHTML += '<li><strong>Consider mentioning these related technologies:</strong> ';
        suggestionsHTML += additionalTech.join(', ');
        suggestionsHTML += '</li>';
    }

    // Add specific suggestions for resume improvements
    suggestionsHTML += '<li><strong>Resume improvements:</strong><ul>';
    suggestionsHTML += '<li>Tailor your experience bullet points to highlight achievements relevant to this role</li>';
    suggestionsHTML += '<li>Prioritize skills mentioned in the job description: ' + keywords.slice(0, 3).join(', ') + '</li>';
    suggestionsHTML += '<li>Quantify your achievements with metrics to show measurable impact</li>';
    suggestionsHTML += '</ul></li>';

    // Add specific suggestions for the intro paragraph
    suggestionsHTML += '<li><strong>Email intro paragraph suggestions:</strong><ul>';
    suggestionsHTML += `<li>Mention your experience with ${keywords[0]} and ${keywords[1] || 'related technologies'}</li>`;
    suggestionsHTML += `<li>Reference specific achievements that align with ${companyName}'s needs</li>`;
    suggestionsHTML += '<li>Show enthusiasm for the specific role rather than just the company</li>';
    suggestionsHTML += '</ul></li>';

    // Add job description analysis
    suggestionsHTML += '<li><strong>Job description analysis:</strong><ul>';
    suggestionsHTML += `<li>Primary focus areas: ${getPrimaryFocusAreas(jobDescription, keywords)}</li>`;
    suggestionsHTML += `<li>Experience level: ${getExperienceLevel(jobDescription)}</li>`;
    suggestionsHTML += `<li>Work environment: ${getWorkEnvironment(jobDescription)}</li>`;
    suggestionsHTML += '</ul></li>';

    suggestionsHTML += '</ul>';

    return suggestionsHTML;
}

// Function to get additional relevant technologies based on the detected keywords
function getAdditionalTechnologies(detectedKeywords, jobDescription) {
    // Detect if this is a construction/trades job
    const isConstructionJob = isConstructionRelated(jobDescription);

    // Function to determine if a job is construction-related
    function isConstructionRelated(description) {
        const constructionTerms = [
            'construction', 'electrician', 'plumber', 'carpenter', 'welder', 'hvac',
            'concrete', 'masonry', 'drywall', 'framing', 'roofing', 'painting',
            'flooring', 'tiling', 'insulation', 'cabinetry', 'millwork', 'glazing',
            'excavation', 'demolition', 'renovation', 'remodeling', 'restoration'
        ];

        const lowerDesc = description.toLowerCase();
        for (const term of constructionTerms) {
            if (lowerDesc.includes(term)) {
                return true;
            }
        }

        return false;
    }

    // Define relationships based on job type
    let techRelationships = {};

    if (isConstructionJob) {
        // Construction and trades relationships
        techRelationships = {
            'Construction': ['Safety Procedures', 'Building Codes', 'Blueprint Reading', 'Material Handling', 'Quality Control'],
            'Electrician': ['Electrical Code', 'Conduit Bending', 'Circuit Testing', 'Troubleshooting', 'Panel Installation'],
            'Plumber': ['Pipe Fitting', 'Fixture Installation', 'Plumbing Code', 'Drain Cleaning', 'Water Heaters'],
            'Carpenter': ['Framing', 'Finish Carpentry', 'Cabinet Installation', 'Trim Work', 'Stair Building'],
            'Concrete': ['Formwork', 'Finishing', 'Curing', 'Reinforcement', 'Concrete Mixing'],
            'Safety': ['Fall Protection', 'WHMIS', 'First Aid', 'PPE', 'Confined Space'],
            'Equipment Operation': ['Forklift', 'Skid Steer', 'Boom Lift', 'Excavator', 'Crane'],
            'Maintenance': ['Preventative Maintenance', 'Troubleshooting', 'Repair', 'Inspection', 'Documentation'],
            'Installation': ['Fixtures', 'Appliances', 'Flooring', 'Cabinetry', 'Windows and Doors'],
            'Commercial': ['Building Systems', 'Code Compliance', 'Project Scheduling', 'Material Estimation', 'Quality Control'],
            'Residential': ['Home Construction', 'Renovation', 'Remodeling', 'Finishing', 'Interior Work']
        };
    } else {
        // Tech and office relationships
        techRelationships = {
            'Python': ['Django', 'Flask', 'Pandas', 'NumPy', 'TensorFlow', 'PyTorch', 'Scikit-learn'],
            'JavaScript': ['React', 'Angular', 'Vue.js', 'Node.js', 'Express', 'TypeScript', 'jQuery'],
            'Java': ['Spring', 'Hibernate', 'Maven', 'Gradle', 'JUnit', 'Tomcat', 'Microservices'],
            'C#': ['.NET Core', 'ASP.NET', 'Entity Framework', 'LINQ', 'WPF', 'Xamarin', 'Unity'],
            'SQL': ['MySQL', 'PostgreSQL', 'SQL Server', 'Oracle', 'NoSQL', 'MongoDB', 'Database Design'],
            'AWS': ['EC2', 'S3', 'Lambda', 'CloudFormation', 'DynamoDB', 'RDS', 'CloudFront'],
            'Azure': ['App Service', 'Functions', 'DevOps', 'Active Directory', 'Cosmos DB', 'Logic Apps'],
            'DevOps': ['CI/CD', 'Jenkins', 'Docker', 'Kubernetes', 'Terraform', 'Ansible', 'GitLab'],
            'Data Analysis': ['Excel', 'Power BI', 'Tableau', 'R', 'SQL', 'Data Visualization', 'Statistical Analysis'],
            'Machine Learning': ['TensorFlow', 'PyTorch', 'Scikit-learn', 'Neural Networks', 'NLP', 'Computer Vision'],
            'Web Development': ['HTML', 'CSS', 'JavaScript', 'React', 'Angular', 'Vue.js', 'Responsive Design'],
            'Mobile Development': ['iOS', 'Android', 'Swift', 'Kotlin', 'React Native', 'Flutter', 'Mobile UI/UX'],
            'Cloud': ['AWS', 'Azure', 'Google Cloud', 'Serverless', 'Microservices', 'Containers', 'IaC'],
            'Excel VBA': ['Macros', 'Automation', 'Data Processing', 'User Forms', 'Reporting'],
            'PySpark': ['Big Data', 'Data Processing', 'ETL', 'Data Analysis', 'Hadoop'],
            'Matplotlib': ['Data Visualization', 'Plotting', 'Charts', 'Graphs', 'Data Analysis'],
            'Automation': ['Scripting', 'Workflow Optimization', 'Process Improvement', 'Efficiency', 'Time Saving']
        };
    }

    // Get additional technologies based on detected keywords
    const additionalTech = new Set();
    detectedKeywords.forEach(keyword => {
        if (techRelationships[keyword]) {
            techRelationships[keyword].forEach(tech => {
                // Only add if not already in detected keywords and not mentioned in job description
                if (!detectedKeywords.includes(tech) && !jobDescription.toLowerCase().includes(tech.toLowerCase())) {
                    additionalTech.add(tech);
                }
            });
        }
    });

    // If no additional tech found for construction jobs, add some general construction skills
    if (isConstructionJob && additionalTech.size === 0) {
        const generalConstructionSkills = [
            'Safety Procedures', 'WHMIS', 'First Aid', 'Blueprint Reading',
            'Material Handling', 'Equipment Maintenance', 'Quality Control'
        ];

        generalConstructionSkills.forEach(skill => {
            if (!detectedKeywords.includes(skill) && !jobDescription.toLowerCase().includes(skill.toLowerCase())) {
                additionalTech.add(skill);
            }
        });
    }

    // Convert to array and limit to 5 suggestions
    return Array.from(additionalTech).slice(0, 5);
}

// Function to determine primary focus areas from job description
function getPrimaryFocusAreas(jobDescription, keywords) {
    // Detect if this is a construction/trades job
    const isConstructionJob = isConstructionRelated(jobDescription);

    // Function to determine if a job is construction-related
    function isConstructionRelated(description) {
        const constructionTerms = [
            'construction', 'electrician', 'plumber', 'carpenter', 'welder', 'hvac',
            'concrete', 'masonry', 'drywall', 'framing', 'roofing', 'painting'
        ];

        const lowerDesc = description.toLowerCase();
        for (const term of constructionTerms) {
            if (lowerDesc.includes(term)) {
                return true;
            }
        }

        return false;
    }

    // Define focus areas based on job type
    let focusAreas = {};

    if (isConstructionJob) {
        focusAreas = {
            'construction': ['build', 'construct', 'erect', 'install', 'assemble'],
            'maintenance': ['maintain', 'repair', 'service', 'fix', 'upkeep'],
            'installation': ['install', 'set up', 'mount', 'place', 'fit'],
            'operation': ['operate', 'run', 'control', 'handle', 'drive'],
            'safety': ['safety', 'protect', 'secure', 'safeguard', 'prevent'],
            'supervision': ['supervise', 'oversee', 'direct', 'manage', 'lead'],
            'inspection': ['inspect', 'check', 'examine', 'test', 'verify'],
            'preparation': ['prepare', 'clean', 'set up', 'ready', 'arrange']
        };
    } else {
        focusAreas = {
            'development': ['develop', 'build', 'code', 'program', 'implement'],
            'analysis': ['analyze', 'research', 'study', 'evaluate', 'assess'],
            'design': ['design', 'architect', 'plan', 'create', 'model'],
            'management': ['manage', 'lead', 'direct', 'supervise', 'coordinate'],
            'support': ['support', 'maintain', 'troubleshoot', 'resolve', 'assist']
        };
    }

    // Count occurrences of focus area keywords
    const counts = {};
    for (const [area, terms] of Object.entries(focusAreas)) {
        counts[area] = 0;
        terms.forEach(term => {
            const regex = new RegExp('\\b' + term + '\\w*\\b', 'gi');
            const matches = jobDescription.match(regex) || [];
            counts[area] += matches.length;
        });
    }

    // Sort by count and get top 2
    const topAreas = Object.entries(counts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 2)
        .map(entry => entry[0]);

    if (topAreas.length === 0) {
        return isConstructionJob ? 'construction and labor' : 'general technical work';
    }

    return topAreas.join(' and ');
}

// Function to determine experience level from job description
function getExperienceLevel(jobDescription) {
    const lowerDesc = jobDescription.toLowerCase();

    if (lowerDesc.includes('senior') || lowerDesc.includes('lead') ||
        lowerDesc.includes('principal') || lowerDesc.includes('architect')) {
        return 'Senior level';
    } else if (lowerDesc.includes('junior') || lowerDesc.includes('entry') ||
               lowerDesc.includes('graduate') || lowerDesc.includes('intern')) {
        return 'Junior/Entry level';
    } else if (lowerDesc.includes('mid') || lowerDesc.includes('intermediate')) {
        return 'Mid level';
    } else {
        // Try to determine by years of experience
        const expMatch = lowerDesc.match(/(\d+)[\+]?\s*(?:to|-)?\s*(\d+)?\s*years?/);
        if (expMatch) {
            const minYears = parseInt(expMatch[1]);
            if (minYears >= 5) {
                return 'Senior level (5+ years)';
            } else if (minYears >= 2) {
                return 'Mid level (2-5 years)';
            } else {
                return 'Junior level (0-2 years)';
            }
        }
    }

    return 'Not specified';
}

// Function to determine work environment from job description
function getWorkEnvironment(jobDescription) {
    const lowerDesc = jobDescription.toLowerCase();

    // Detect if this is a construction/trades job
    const isConstructionJob = isConstructionRelated(jobDescription);

    // Function to determine if a job is construction-related
    function isConstructionRelated(description) {
        const constructionTerms = [
            'construction', 'electrician', 'plumber', 'carpenter', 'welder', 'hvac',
            'concrete', 'masonry', 'drywall', 'framing', 'roofing', 'painting'
        ];

        const lowerDesc = description.toLowerCase();
        for (const term of constructionTerms) {
            if (lowerDesc.includes(term)) {
                return true;
            }
        }

        return false;
    }

    // Construction jobs are almost always on-site
    if (isConstructionJob) {
        // Still check for explicit mentions
        if (lowerDesc.includes('remote') || lowerDesc.includes('work from home') ||
            lowerDesc.includes('telecommute') || lowerDesc.includes('virtual')) {
            return 'Remote (unusual for this type of work)';
        } else if (lowerDesc.includes('hybrid')) {
            return 'Hybrid (unusual for this type of work)';
        } else {
            // For construction jobs, assume on-site if not explicitly stated otherwise
            return 'On-site (typical for construction work)';
        }
    } else {
        // For non-construction jobs, use standard detection
        if (lowerDesc.includes('remote') || lowerDesc.includes('work from home') ||
            lowerDesc.includes('telecommute') || lowerDesc.includes('virtual')) {
            return 'Remote';
        } else if (lowerDesc.includes('hybrid')) {
            return 'Hybrid';
        } else if (lowerDesc.includes('office') || lowerDesc.includes('on-site') ||
                lowerDesc.includes('on site') || lowerDesc.includes('in-person')) {
            return 'On-site';
        }
    }

    return 'Not specified';
}

// Function to extract keywords from job description
function extractKeywords(jobDescription) {
    // Detect if this is a construction/trades job
    const isConstructionJob = isConstructionRelated(jobDescription);

    // Skills to look for based on job type
    let skillsToLookFor = [];

    if (isConstructionJob) {
        // Construction and trades skills
        skillsToLookFor = [
            // Construction roles
            'Construction', 'Electrician', 'Plumber', 'Carpenter', 'Welder', 'HVAC',
            'Concrete', 'Masonry', 'Drywall', 'Framing', 'Roofing', 'Painting',
            'Flooring', 'Tiling', 'Insulation', 'Cabinetry', 'Millwork', 'Glazing',
            'Excavation', 'Demolition', 'Renovation', 'Remodeling', 'Restoration',

            // Construction equipment
            'Forklift', 'Crane', 'Bulldozer', 'Excavator', 'Loader', 'Backhoe',
            'Skid Steer', 'Scissor Lift', 'Boom Lift', 'Scaffolding', 'Power Tools',

            // Construction skills
            'Blueprint Reading', 'Estimating', 'Scheduling', 'Safety', 'OSHA',
            'Building Codes', 'Inspection', 'Quality Control', 'Project Management',

            // Materials
            'Lumber', 'Steel', 'Concrete', 'Drywall', 'Insulation', 'Roofing Materials',
            'Plumbing Fixtures', 'Electrical Components', 'HVAC Systems',

            // Certifications
            'Red Seal', 'Journeyman', 'Apprentice', 'First Aid', 'WHMIS', 'Fall Protection',
            'Confined Space', 'Traffic Control', 'Equipment Certification',

            // General construction terms
            'Residential', 'Commercial', 'Industrial', 'Maintenance', 'Repair',
            'Installation', 'Troubleshooting', 'Finishing', 'Framing', 'Foundation'
        ];
    } else {
        // Tech and office skills
        skillsToLookFor = [
            'Python', 'JavaScript', 'HTML', 'CSS', 'SQL', 'Excel', 'VBA',
            'C#', '.NET', 'AWS', 'Azure', 'Firebase', 'SharePoint', 'Tableau',
            'Power BI', 'JIRA', 'Agile', 'Scrum', 'Data Analysis', 'Machine Learning',
            'AI', 'Automation', 'API', 'REST', 'JSON', 'XML', 'Git', 'DevOps',
            'CI/CD', 'Docker', 'Kubernetes', 'Cloud', 'Database', 'NoSQL', 'MongoDB',
            'PostgreSQL', 'MySQL', 'Oracle', 'React', 'Angular', 'Vue', 'Node.js',
            'Express', 'Django', 'Flask', 'Spring', 'Java', 'C++', 'Ruby', 'PHP',
            'Swift', 'Kotlin', 'Mobile', 'iOS', 'Android', 'UI/UX', 'Design',
            'Testing', 'QA', 'Security', 'Networking', 'Linux', 'Windows', 'MacOS',
            'Shell', 'Bash', 'PowerShell', 'ETL', 'Data Warehouse', 'Big Data',
            'Hadoop', 'Spark', 'Kafka', 'ELK', 'Grafana', 'Prometheus', 'Monitoring',
            'Analytics', 'Reporting', 'Business Intelligence', 'Product Management',
            'Project Management', 'Scrum Master', 'Product Owner', 'Technical Writing',
            'Documentation', 'Support', 'Customer Service', 'Sales', 'Marketing'
        ];
    }

    // Function to determine if a job is construction-related
    function isConstructionRelated(description) {
        const constructionTerms = [
            'construction', 'electrician', 'plumber', 'carpenter', 'welder', 'hvac',
            'concrete', 'masonry', 'drywall', 'framing', 'roofing', 'painting',
            'flooring', 'tiling', 'insulation', 'cabinetry', 'millwork', 'glazing',
            'excavation', 'demolition', 'renovation', 'remodeling', 'restoration',
            'forklift', 'crane', 'bulldozer', 'excavator', 'loader', 'backhoe',
            'skid steer', 'scissor lift', 'boom lift', 'scaffolding', 'power tools',
            'blueprint', 'building codes', 'osha', 'safety', 'inspection',
            'lumber', 'steel', 'plumbing', 'electrical', 'hvac',
            'red seal', 'journeyman', 'apprentice', 'whmis', 'fall protection',
            'residential', 'commercial', 'industrial', 'maintenance', 'repair',
            'installation', 'troubleshooting', 'finishing', 'foundation'
        ];

        const lowerDesc = description.toLowerCase();
        for (const term of constructionTerms) {
            if (lowerDesc.includes(term)) {
                return true;
            }
        }

        return false;
    }

    // Find matches in job description
    const matches = [];
    skillsToLookFor.forEach(skill => {
        if (jobDescription.toLowerCase().includes(skill.toLowerCase())) {
            matches.push(skill);
        }
    });

    // If no matches found, return some default skills based on job type
    if (matches.length === 0) {
        if (isConstructionJob) {
            return ['Construction', 'Safety', 'Equipment Operation', 'Maintenance', 'Installation'];
        } else {
            return ['Python', 'SQL', 'Data Analysis', 'Excel', 'Automation'];
        }
    }

    return matches;
}

// Function to customize resume based on job description
function customizeResume(jobDescription) {
    // This function is now simplified since we removed the resume preview
    // We'll just extract keywords for use in the email
    const keywords = extractKeywords(jobDescription);
    console.log("Keywords extracted for email:", keywords);

    // In a full implementation, you might want to use these keywords
    // to customize the email content further
}

// Function to extract company name and email from job description
function extractCompanyInfo(jobDescription) {
    let companyName = '';
    let recruiterEmail = '';

    // Common company identifiers in job descriptions
    const companyIdentifiers = [
        'at', 'with', 'for', 'join', 'company:', 'organization:', 'employer:',
        'about us', 'about the company', 'about the organization', 'about the employer',
        'company overview', 'organization overview', 'employer overview',
        'employer details'
    ];

    // Try to extract company name
    const lines = jobDescription.split('\n');

    // Special case for "Employer details" pattern
    const employerDetailsPattern = /employer\s*details\s*(.+)/i;
    for (const line of lines) {
        const match = line.match(employerDetailsPattern);
        if (match && match[1]) {
            companyName = match[1].trim();
            break;
        }
    }

    // Special case for "Posted on [date] by Employer details[Company]" pattern
    const postedByPattern = /posted\s+on.*by\s+employer\s+details\s*(.+)/i;
    for (const line of lines) {
        const match = line.match(postedByPattern);
        if (match && match[1]) {
            companyName = match[1].trim();
            break;
        }
    }

    // Handle the specific format from the example
    // Look for lines with "Employer details" followed by company name on the same or next line
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].toLowerCase().includes("employer details")) {
            // Check if company name is on the same line after "Employer details"
            const parts = lines[i].split(/employer\s+details/i);
            if (parts.length > 1 && parts[1].trim()) {
                companyName = parts[1].trim();
                break;
            }

            // If not, check the next line for the company name
            if (i + 1 < lines.length && lines[i + 1].trim()) {
                companyName = lines[i + 1].trim();
                break;
            }
        }
    }

    // Handle the specific example: "carpenter Posted on April 22, 2025 by Employer detailsApex Contractor & Developer Inc."
    const carpenterPattern = /Posted on .* by Employer details([A-Za-z0-9\s&]+)/i;
    const carpenterMatch = jobDescription.match(carpenterPattern);
    if (carpenterMatch && carpenterMatch[1]) {
        companyName = carpenterMatch[1].trim();
    }

    // Try one more pattern for the specific example
    if (!companyName) {
        // Look for "Employer details" without spaces before company name
        const noSpacePattern = /Employer details([A-Za-z0-9\s&]+)/i;
        const noSpaceMatch = jobDescription.match(noSpacePattern);
        if (noSpaceMatch && noSpaceMatch[1]) {
            companyName = noSpaceMatch[1].trim();
        }
    }

    // If no match found with the special pattern, try common patterns
    if (!companyName) {
        for (const line of lines) {
            const lowerLine = line.toLowerCase();

            // Check for lines that likely contain the company name
            for (const identifier of companyIdentifiers) {
                if (lowerLine.includes(identifier)) {
                    // Extract text after the identifier
                    const parts = line.split(new RegExp(`${identifier}\\s+`, 'i'));
                    if (parts.length > 1) {
                        // Extract up to the first punctuation or end of line
                        const potentialName = parts[1].split(/[,.;:]|\bis\b|\bwe\b/)[0].trim();
                        if (potentialName && potentialName.length > 2 && potentialName.length < 50) {
                            companyName = potentialName;
                            break;
                        }
                    }
                }
            }

            if (companyName) break;
        }
    }

    // If no company name found, look for capitalized words that might be a company name
    if (!companyName) {
        const capitalizedWordPattern = /\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b/g;
        const capitalizedMatches = jobDescription.match(capitalizedWordPattern);

        if (capitalizedMatches && capitalizedMatches.length > 0) {
            // Find the most frequent capitalized phrase that's likely a company name
            const wordCounts = {};
            for (const match of capitalizedMatches) {
                if (match.length > 3 && !['The', 'And', 'For', 'With', 'About'].includes(match)) {
                    wordCounts[match] = (wordCounts[match] || 0) + 1;
                }
            }

            // Find the most frequent capitalized phrase
            let maxCount = 0;
            for (const word in wordCounts) {
                if (wordCounts[word] > maxCount) {
                    maxCount = wordCounts[word];
                    companyName = word;
                }
            }
        }
    }

    // Extract email addresses
    const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    const emailMatches = jobDescription.match(emailPattern);

    if (emailMatches && emailMatches.length > 0) {
        // Use the first email found
        recruiterEmail = emailMatches[0];
    }

    // If still no company name, use a default
    if (!companyName) {
        companyName = "the Company";
    }

    // Log the extracted information for debugging
    console.log("Extracted company name:", companyName);
    console.log("Extracted email:", recruiterEmail);

    return { companyName, recruiterEmail };
}

// Function to generate email template
function generateEmail(companyName, recruiterEmail) {
    console.log("Generating email for company:", companyName);
    console.log("Recruiter email:", recruiterEmail);

    // Set email recipient
    document.getElementById('email-to').value = recruiterEmail || '';

    // Set email subject
    document.getElementById('email-subject').value = `Application for Position at ${companyName}`;

    // Extract keywords from job description to personalize email
    const jobDesc = document.getElementById('job-desc-input').value;
    const keywords = extractKeywords(jobDesc);

    // Get top skills to mention
    const topSkills = keywords.slice(0, 5).join(', ');
    console.log("Top skills:", topSkills);

    // Use Mathew's information for the signature
    const userName = 'Mathew Kasbarian';
    const userPhone = '6479639291';
    const userEmail = '<EMAIL>';
    console.log("User info:", userName, userPhone, userEmail);

    // Detect if this is a construction/trades job
    const isConstructionJob = isConstructionRelated(jobDesc);

    // Function to determine if a job is construction-related
    function isConstructionRelated(description) {
        const constructionTerms = [
            'construction', 'electrician', 'plumber', 'carpenter', 'welder', 'hvac',
            'concrete', 'masonry', 'drywall', 'framing', 'roofing', 'painting'
        ];

        const lowerDesc = description.toLowerCase();
        for (const term of constructionTerms) {
            if (lowerDesc.includes(term)) {
                return true;
            }
        }

        return false;
    }

    // Generate email body based on job type
    let emailBody = '';

    if (isConstructionJob) {
        // Construction/trades email template
        emailBody = `
            <p>Dear Hiring Manager at ${companyName},</p>

            <p>I am writing to apply for the construction position advertised at ${companyName}. With hands-on experience in ${topSkills.toLowerCase()}, I am confident in my ability to contribute effectively to your team.</p>

            <p>Throughout my career in construction, I have developed strong skills in safety procedures, efficient work practices, and quality workmanship. I understand the importance of reliability, punctuality, and teamwork on construction sites, and I take pride in completing projects to high standards.</p>

            <p>I have attached my resume (Mathew_Kasbarian_Resume_Transformed.pdf) for your review, which I've customized specifically for this position to highlight my relevant experience and qualifications. I am available to start immediately and can provide references upon request.</p>

            <p>Thank you for considering my application. I look forward to the opportunity to discuss how my skills and experience can benefit your team at ${companyName}.</p>

            <p>Sincerely,<br>
            ${userName}<br>
            ${userPhone}<br>
            ${userEmail}</p>
        `;
    } else {
        // Tech/office job email template
        emailBody = `
            <p>Dear Hiring Manager at ${companyName},</p>

            <p>I hope this email finds you well. I am writing to express my interest in the position advertised at ${companyName}. After reviewing the job description, I believe my skills and experience align well with what you're looking for.</p>

            <p>Based on your job posting, I understand you're looking for someone with expertise in ${topSkills}. My background and experience have prepared me well for this role, and I'm confident I can make valuable contributions to your team at ${companyName}.</p>

            <p>I have attached my resume (Mathew_Kasbarian_Resume_Transformed.pdf) for your review, which I've customized specifically for this position to highlight my relevant experience and qualifications. I would welcome the opportunity to discuss how my background, skills, and achievements can benefit ${companyName}.</p>

            <p>Thank you for considering my application. I look forward to the possibility of speaking with you soon about this exciting opportunity at ${companyName}.</p>

            <p>Best regards,<br>
            ${userName}<br>
            ${userPhone}<br>
            ${userEmail}</p>
        `;
    }

    console.log("Setting email body");
    document.getElementById('email-body').innerHTML = emailBody;
    console.log("Email body set");
}

// Function to send email with attachment
async function sendEmail() {
    const emailTo = document.getElementById('email-to').value;
    const emailSubject = document.getElementById('email-subject').value;
    const emailBody = document.getElementById('email-body').innerText;

    if (!emailTo) {
        alert('No recipient email address found. Please make sure a recipient email is available.');
        return;
    }

    // Show loading state
    const sendEmailBtn = document.getElementById('send-email');
    const originalBtnText = sendEmailBtn.textContent;
    sendEmailBtn.textContent = 'Sending...';
    sendEmailBtn.disabled = true;

    try {
        // Check if we have a transformed resume
        const resumePreview = document.getElementById('resume-preview');
        if (!resumePreview || !resumePreview.innerHTML.trim()) {
            // If no transformed resume, automatically transform it
            alert('Transforming your resume to match the job description...');
            // Switch to transform tab and transform resume
            document.querySelector('[data-tab="transformed-resume"]').click();
            await transformResume();

            // Wait for transformation to complete
            await new Promise(resolve => setTimeout(resolve, 3500));

            // Switch back to email tab
            document.querySelector('[data-tab="email-template"]').click();
        }

        // Use stored PDF data or generate new PDF
        let pdfData = window.resumePdfData || '';

        // If no stored PDF data, generate it now
        if (!pdfData && resumePreview && resumePreview.innerHTML.trim()) {
            try {
                // Show generating PDF message
                alert('Generating PDF from your transformed resume...');

                // Create a new jsPDF instance
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();

                // Use html2canvas to capture the resume as an image
                const canvas = await html2canvas(resumePreview);
                const imgData = canvas.toDataURL('image/png');
                const imgWidth = 210; // A4 width in mm
                const pageHeight = 295; // A4 height in mm
                const imgHeight = canvas.height * imgWidth / canvas.width;
                let heightLeft = imgHeight;
                let position = 0;

                // Add image to PDF
                doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;

                // Add new pages if needed for longer resumes
                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight;
                    doc.addPage();
                    doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;
                }

                // Get PDF as base64 string
                pdfData = doc.output('datauristring');

                // Store for future use
                window.resumePdfData = pdfData;
            } catch (error) {
                console.error('Error generating PDF:', error);
                alert('There was an error generating the PDF. The email will be sent without an attachment.');
            }
        }

        // Send the email with attachment via our server
        const response = await fetch('/send-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                to: emailTo,
                subject: emailSubject,
                body: emailBody,
                pdfData: pdfData
            }),
        });

        const result = await response.json();

        if (result.success) {
            alert('Email sent successfully with your transformed resume attached!');
        } else {
            // If server-side sending fails, fall back to mailto
            alert('Server-side email sending failed. Opening your default email client instead.');

            // Create a mailto link
            const mailtoLink = `mailto:${encodeURIComponent(emailTo)}?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;

            // Open the link
            window.open(mailtoLink);

            // Show a reminder about attaching the resume
            setTimeout(() => {
                alert('Remember to attach your transformed resume (Mathew_Kasbarian_Resume_Transformed.pdf) to the email before sending!');
            }, 1000);
        }
    } catch (error) {
        console.error('Error sending email:', error);

        // Fall back to mailto
        alert('There was an error sending the email. Opening your default email client instead.');

        // Create a mailto link
        const mailtoLink = `mailto:${encodeURIComponent(emailTo)}?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;

        // Open the link
        window.open(mailtoLink);

        // Show a reminder about attaching the resume
        setTimeout(() => {
            alert('Remember to attach your transformed resume (Mathew_Kasbarian_Resume_Transformed.pdf) to the email before sending!');
        }, 1000);
    } finally {
        // Reset button state
        sendEmailBtn.textContent = originalBtnText;
        sendEmailBtn.disabled = false;
    }
}

// Function to transform resume using AI
function transformResume() {
    const jobDescription = document.getElementById('job-desc-input').value;

    if (!jobDescription) {
        alert('Please paste a job description first.');
        return;
    }

    // Show loading state
    document.getElementById('transform-resume-btn').textContent = 'Transforming...';
    document.getElementById('transform-resume-btn').disabled = true;

    // Switch to the transformed resume tab
    document.querySelector('[data-tab="transformed-resume"]').click();

    // Show loading spinner
    document.getElementById('resume-loading').style.display = 'block';
    document.getElementById('resume-not-transformed').style.display = 'none';
    document.getElementById('resume-transformed').style.display = 'none';

    // Extract keywords and company info
    const keywords = extractKeywords(jobDescription);
    const extractedInfo = extractCompanyInfo(jobDescription);
    const companyName = extractedInfo.companyName;

    // Simulate AI processing (in a real app, this would call an AI API)
    setTimeout(() => {
        // Generate transformed resume
        const transformedResume = generateTransformedResume(jobDescription, keywords, companyName);

        // Generate improvement suggestions
        const improvements = generateResumeImprovements(jobDescription, keywords);

        // Update the UI
        document.getElementById('resume-preview').innerHTML = transformedResume;

        // Populate improvements list
        const improvementsList = document.getElementById('resume-improvements-list');
        improvementsList.innerHTML = '';
        improvements.forEach(improvement => {
            const li = document.createElement('li');
            li.textContent = improvement;
            improvementsList.appendChild(li);
        });

        // Hide loading, show transformed resume
        document.getElementById('resume-loading').style.display = 'none';
        document.getElementById('resume-transformed').style.display = 'block';

        // Reset button state
        document.getElementById('transform-resume-btn').textContent = 'Transform My Resume with AI';
        document.getElementById('transform-resume-btn').disabled = false;
    }, 3000);
}

// Function to generate transformed resume HTML
function generateTransformedResume(jobDescription, keywords, companyName) {
    // Detect if this is a construction/trades job
    const isConstructionJob = isConstructionRelated(jobDescription);

    // Function to determine if a job is construction-related
    function isConstructionRelated(description) {
        const constructionTerms = [
            'construction', 'electrician', 'plumber', 'carpenter', 'welder', 'hvac',
            'concrete', 'masonry', 'drywall', 'framing', 'roofing', 'painting'
        ];

        const lowerDesc = description.toLowerCase();
        for (const term of constructionTerms) {
            if (lowerDesc.includes(term)) {
                return true;
            }
        }

        return false;
    }

    // Basic resume template
    let resumeHTML = '';

    if (isConstructionJob) {
        // Construction resume template
        resumeHTML = `
            <div class="resume-header">
                <h1>MATHEW KASBARIAN</h1>
                <div class="resume-contact">
                    <div class="contact-left">
                        <p>Markham, ON</p>
                        <p>647-963-9291</p>
                        <p><EMAIL></p>
                    </div>
                </div>
            </div>

            <div class="resume-summary">
                <p>Dedicated and safety-conscious construction professional with experience in ${keywords.slice(0, 3).join(', ')}.
                Strong track record of completing projects on time and to high quality standards.
                Seeking to leverage my skills and experience to contribute to ${companyName}'s projects.</p>
            </div>

            <div class="resume-section">
                <h2>EDUCATION</h2>
                <div class="education-item">
                    <p><strong>Ontario Tech University</strong></p>
                    <p>Bachelor of Science (Hons) Computer Science</p>
                    <p>Data Science (Minor)</p>
                </div>
            </div>

            <div class="resume-section">
                <h2>SKILLS</h2>
                <div class="skills-grid">
                    <div class="skills-column">
                        <p>Construction</p>
                        <p>Safety Procedures</p>
                        <p>Equipment Operation</p>
                        <p>Quality Control</p>
                    </div>
                    <div class="skills-column">
                        <p>Project Management</p>
                        <p>Team Collaboration</p>
                        <p>Problem Solving</p>
                        <p>Technical Documentation</p>
                    </div>
                </div>
            </div>

            <div class="resume-section">
                <h2>EXPERIENCE</h2>
                <div class="experience-item">
                    <div class="job-header">
                        <p class="job-title"><strong>Construction Specialist</strong></p>
                        <p class="job-company">PCL Construction | 2020 - Present</p>
                    </div>
                    <ul class="job-duties">
                        <li>Successfully completed over 20 construction projects, ensuring adherence to safety protocols and quality standards.</li>
                        <li>Operated and maintained various construction equipment, including ${keywords.includes('Equipment Operation') ? 'heavy machinery' : 'power tools'}.</li>
                        <li>Collaborated with team members to complete projects efficiently and on schedule.</li>
                        <li>Implemented safety procedures that resulted in zero workplace incidents over a 2-year period.</li>
                    </ul>
                </div>
                <div class="experience-item">
                    <div class="job-header">
                        <p class="job-title"><strong>Construction Assistant</strong></p>
                        <p class="job-company">EllisDon Corporation | 2018 - 2020</p>
                    </div>
                    <ul class="job-duties">
                        <li>Assisted in various construction tasks including site preparation, material handling, and cleanup.</li>
                        <li>Supported senior team members in completing construction projects on time and within budget.</li>
                        <li>Maintained tools and equipment to ensure optimal performance and longevity.</li>
                    </ul>
                </div>
            </div>
        `;
    } else {
        // Tech resume template - matching the Word doc format
        resumeHTML = `
            <div class="resume-header">
                <h1>MATHEW KASBARIAN B.SC.</h1>
                <div class="resume-summary">
                    <p>Developer with a B.Sc. in Computer Science and a Data Science minor. I specialize in ${keywords.slice(0, 3).join(', ')},
                    building scalable software, and streamlining operations using Excel VBA, Python, C#, SQL, JavaScript, HTML and CSS.
                    Passionate about saving organizations time through automation and empowering decision-makers with real-time, actionable data.</p>
                </div>
                <div class="resume-contact">
                    <div class="contact-right">
                        <p><strong>CONTACT</strong></p>
                        <p>Markham, ON</p>
                        <p>647-963-9291</p>
                        <p><EMAIL></p>
                    </div>
                </div>
            </div>

            <div class="resume-section">
                <h2>EDUCATION</h2>
                <div class="education-item">
                    <p><strong>Ontario Tech University</strong></p>
                    <p>Bachelor of Science (Hons) Computer Science</p>
                    <p>Data Science (Minor)</p>
                </div>
            </div>

            <div class="resume-section">
                <h2>TECHNOLOGIES & TOOLS</h2>
                <div class="tech-section">
                    <p><strong>Programming Languages:</strong><br>
                    ${generateTechSkillsList(keywords, jobDescription, 'languages')}</p>

                    <p><strong>Web Development:</strong><br>
                    ${generateTechSkillsList(keywords, jobDescription, 'web')}</p>

                    <p><strong>Frameworks & Platforms:</strong><br>
                    ${generateTechSkillsList(keywords, jobDescription, 'frameworks')}</p>

                    <p><strong>Databases:</strong><br>
                    ${generateTechSkillsList(keywords, jobDescription, 'databases')}</p>
                </div>
            </div>

            <div class="resume-section">
                <h2>SKILLS</h2>
                <div class="skills-grid">
                    <div class="skills-column">
                        <p>Analysis & Automation</p>
                        <p>Data Collection & Integration</p>
                    </div>
                    <div class="skills-column">
                        <p>Agile Methodology</p>
                        <p>Scalable Software Development</p>
                    </div>
                </div>
            </div>

            <div class="resume-section">
                <h2>EXPERIENCE</h2>
                <div class="experience-item">
                    <div class="job-header">
                        <p class="job-title"><strong>TORONTO AUTOMATIC DOORS INC. – Software Developer</strong></p>
                        <p class="job-date">December 2024 – PRESENT</p>
                    </div>
                    <ul class="job-duties">
                        <li>Automated creation of Estimates, POs, and Invoices in QuickBooks using C# (.NET) and Intuit REST APIs, saving 1,560+ hours annually and contributing to a 15% sales increase.</li>
                        <li>Streamlined client onboarding by automating XML form generation with HTML, CSS, and JavaScript, reducing manual errors.</li>
                        <li>Enabled real-time KPI Dashboard and custom scripting using Excel VBA, PySpark, and Matplotlib.</li>
                    </ul>
                </div>
                <div class="experience-item">
                    <div class="job-header">
                        <p class="job-title"><strong>ROYAL BANK OF CANADA – PROGRAMMER ANALYST</strong></p>
                        <p class="job-date">MAY 2023 - AUGUST 2023, MARCH 2024 – OCTOBER 2024</p>
                    </div>
                    <ul class="job-duties">
                        <li>Automated loan approvals and tracking using Excel VBA, saving 720+ hours annually.</li>
                        <li>Built a real-time KPI dashboard with Excel VBA, PySpark and Matplotlib to support executive decision-making.</li>
                        <li>Managed global SharePoint systems and integrated SQL databases to streamline collaboration, data processing and cross team file management.</li>
                    </ul>
                </div>
                <div class="experience-item">
                    <div class="job-header">
                        <p class="job-title"><strong>GOODLIFE FITNESS - JUNIOR FRONTEND ENGINEER</strong></p>
                        <p class="job-date">JANUARY 2022– AUGUST 2022</p>
                    </div>
                    <ul class="job-duties">
                        <li>Developed a booking and payment system integrated into the company website using Stripe API and PostgreSQL, saving 240+ hours annually.</li>
                        <li>Designed, deployed, and maintained a responsive website using HTML, CSS, and JavaScript, while gaining hands-on experience with agile development.</li>
                    </ul>
                </div>
            </div>
        `;
    }

    return resumeHTML;
}

// Function to generate construction skills list
function generateConstructionSkillsList(keywords, jobDescription) {
    // Get additional relevant skills
    const additionalSkills = getAdditionalTechnologies(keywords, jobDescription);

    // Combine keywords and additional skills
    const allSkills = [...keywords.slice(0, 6), ...additionalSkills.slice(0, 4)];

    // Generate HTML for skills list
    let skillsHTML = '';
    allSkills.forEach(skill => {
        skillsHTML += `<li>${skill}</li>`;
    });

    return skillsHTML;
}

// Function to generate tech skills list by category
function generateTechSkillsList(keywords, jobDescription, category) {
    // Define skills by category
    const skillsByCategory = {
        'languages': ['Python', 'JavaScript', 'Java', 'C#', 'C++', 'R', 'SQL', 'Dart', 'VBA'],
        'web': ['HTML', 'CSS', 'JavaScript', 'XML', 'React', 'Vue', 'Angular'],
        'frameworks': ['.NET', 'Flutter', 'Firebase', 'AWS', 'Azure', 'Google Cloud'],
        'databases': ['PostgreSQL', 'MongoDB', 'Oracle', 'MySQL', 'SQL Server', 'NoSQL']
    };

    // Filter keywords by category
    const categorySkills = keywords.filter(keyword =>
        skillsByCategory[category].some(skill =>
            skill.toLowerCase() === keyword.toLowerCase()
        )
    );

    // Add some default skills if not enough found
    if (categorySkills.length < 3) {
        const defaultSkills = {
            'languages': ['Python', 'JavaScript', 'SQL'],
            'web': ['HTML', 'CSS', 'JavaScript'],
            'frameworks': ['.NET', 'React', 'AWS'],
            'databases': ['PostgreSQL', 'MongoDB', 'SQL Server']
        };

        // Add default skills that aren't already in categorySkills
        defaultSkills[category].forEach(skill => {
            if (!categorySkills.includes(skill)) {
                categorySkills.push(skill);
            }
        });
    }

    // Return comma-separated list
    return categorySkills.slice(0, 5).join(', ');
}

// Function to generate resume improvement suggestions
function generateResumeImprovements(jobDescription, keywords) {
    const improvements = [
        `Highlighted key skills that match the job requirements: ${keywords.slice(0, 3).join(', ')}`,
        'Quantified achievements with specific metrics to demonstrate impact',
        'Tailored professional summary to align with the company\'s needs',
        'Reorganized skills section to prioritize most relevant competencies',
        'Used action verbs to describe accomplishments rather than just responsibilities'
    ];

    return improvements;
}

// Function to download resume as PDF
async function downloadResumePdf() {
    // Create a new jsPDF instance
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
    });

    // Get the resume content
    const resumeElement = document.getElementById('resume-preview');

    try {
        // Show loading state
        const downloadBtn = document.getElementById('download-resume-pdf');
        const originalBtnText = downloadBtn.textContent;
        downloadBtn.textContent = 'Generating PDF...';
        downloadBtn.disabled = true;

        // Temporarily adjust the resume element for better PDF generation
        const originalWidth = resumeElement.style.width;
        const originalPadding = resumeElement.style.padding;
        const originalFontSize = resumeElement.style.fontSize;

        // Set optimal styles for PDF generation
        resumeElement.style.width = '800px';
        resumeElement.style.padding = '10mm';
        resumeElement.style.fontSize = '12px';

        // Use html2canvas with higher quality settings
        const canvas = await html2canvas(resumeElement, {
            scale: 2, // Higher scale for better quality
            useCORS: true,
            logging: false,
            letterRendering: true,
            allowTaint: true
        });

        // Restore original styles
        resumeElement.style.width = originalWidth;
        resumeElement.style.padding = originalPadding;
        resumeElement.style.fontSize = originalFontSize;

        // Calculate dimensions to fit on one page
        const imgData = canvas.toDataURL('image/jpeg', 1.0); // Use JPEG with max quality
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();

        // Calculate the height to maintain aspect ratio
        const ratio = canvas.width / canvas.height;
        const imgWidth = pageWidth - 20; // Leave 10mm margin on each side
        const imgHeight = imgWidth / ratio;

        // Position the image centered with margins
        const xPos = 10; // 10mm from left
        const yPos = 10; // 10mm from top

        // Add image to PDF with proper positioning
        doc.addImage(imgData, 'JPEG', xPos, yPos, imgWidth, imgHeight);

        // Save the PDF
        doc.save('Mathew_Kasbarian_Resume_Transformed.pdf');

        // Store the PDF data for email attachment
        window.resumePdfData = doc.output('datauristring');

        // Show success message
        alert('Resume PDF downloaded successfully! It will be automatically attached when you send an email.');

        // Reset button state
        downloadBtn.textContent = originalBtnText;
        downloadBtn.disabled = false;

        // Automatically go to email tab after downloading
        setTimeout(() => {
            document.querySelector('[data-tab="email-template"]').click();
        }, 1000);
    } catch (error) {
        console.error('Error generating PDF:', error);
        alert('There was an error generating the PDF. Please try again.');

        // Reset button state if there was an error
        const downloadBtn = document.getElementById('download-resume-pdf');
        downloadBtn.textContent = 'Download as PDF';
        downloadBtn.disabled = false;
    }
}

// Function to apply resume to email
async function applyToEmail() {
    // Store the resume PDF data for email attachment if not already done
    if (!window.resumePdfData) {
        const resumeElement = document.getElementById('resume-preview');
        if (resumeElement && resumeElement.innerHTML.trim()) {
            try {
                // Create a new jsPDF instance with better settings
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF({
                    orientation: 'portrait',
                    unit: 'mm',
                    format: 'a4'
                });

                // Temporarily adjust the resume element for better PDF generation
                const originalWidth = resumeElement.style.width;
                const originalPadding = resumeElement.style.padding;
                const originalFontSize = resumeElement.style.fontSize;

                // Set optimal styles for PDF generation
                resumeElement.style.width = '800px';
                resumeElement.style.padding = '10mm';
                resumeElement.style.fontSize = '12px';

                // Use html2canvas with higher quality settings
                const canvas = await html2canvas(resumeElement, {
                    scale: 2, // Higher scale for better quality
                    useCORS: true,
                    logging: false,
                    letterRendering: true,
                    allowTaint: true
                });

                // Restore original styles
                resumeElement.style.width = originalWidth;
                resumeElement.style.padding = originalPadding;
                resumeElement.style.fontSize = originalFontSize;

                // Calculate dimensions to fit on one page
                const imgData = canvas.toDataURL('image/jpeg', 1.0); // Use JPEG with max quality
                const pageWidth = doc.internal.pageSize.getWidth();
                const pageHeight = doc.internal.pageSize.getHeight();

                // Calculate the height to maintain aspect ratio
                const ratio = canvas.width / canvas.height;
                const imgWidth = pageWidth - 20; // Leave 10mm margin on each side
                const imgHeight = imgWidth / ratio;

                // Position the image centered with margins
                const xPos = 10; // 10mm from left
                const yPos = 10; // 10mm from top

                // Add image to PDF with proper positioning
                doc.addImage(imgData, 'JPEG', xPos, yPos, imgWidth, imgHeight);

                // Store the PDF data for email attachment
                window.resumePdfData = doc.output('datauristring');

                // Also save the PDF file for reference
                doc.save('Mathew_Kasbarian_Resume_Transformed.pdf');

                // Navigate to email tab
                document.querySelector('[data-tab="email-template"]').click();
            } catch (error) {
                console.error('Error generating PDF:', error);
                alert('There was an error generating the PDF. Please try again.');
            }
        } else {
            alert('Please transform your resume first before applying to email.');
        }
    } else {
        // If PDF data already exists, just navigate to email tab
        document.querySelector('[data-tab="email-template"]').click();
    }
}

// Function to ensure email is generated when switching to email tab
function ensureEmailGenerated() {
    console.log("Ensuring email is generated...");
    const emailBody = document.getElementById('email-body').innerHTML.trim();
    console.log("Current email body:", emailBody);

    // If email body is empty or contains only placeholder, generate it
    if (!emailBody || emailBody === '<p>|</p>' || emailBody === '|' || emailBody.includes('[Your Name]')) {
        console.log("Email body is empty or contains placeholder, generating new email...");
        const jobDescription = document.getElementById('job-desc-input').value;

        if (jobDescription) {
            console.log("Job description found, extracting company info...");
            // Extract company info
            const extractedInfo = extractCompanyInfo(jobDescription);
            const companyName = extractedInfo.companyName;
            const recruiterEmail = extractedInfo.recruiterEmail;

            console.log("Extracted company name:", companyName);
            console.log("Extracted recruiter email:", recruiterEmail);

            // Generate email
            if (companyName) {
                console.log("Generating email with company name:", companyName);
                generateEmail(companyName, recruiterEmail);
            } else {
                console.log("No company name found, using default");
                generateEmail("the Company", recruiterEmail);
            }
        } else {
            console.log("No job description found");
        }
    } else {
        console.log("Email already generated, skipping");
    }
}

// Function to load user settings from localStorage
function loadUserSettings() {
    // Get saved values
    const userName = localStorage.getItem('userName') || '';
    const userPhone = localStorage.getItem('userPhone') || '';
    const userEmail = localStorage.getItem('userEmail') || '';

    // Set input values
    document.getElementById('user-name').value = userName;
    document.getElementById('user-phone').value = userPhone;
    document.getElementById('user-email').value = userEmail;
}

// Function to save user settings to localStorage
function saveUserSettings() {
    // Get input values
    const userName = document.getElementById('user-name').value;
    const userPhone = document.getElementById('user-phone').value;
    const userEmail = document.getElementById('user-email').value;

    // Save to localStorage
    localStorage.setItem('userName', userName);
    localStorage.setItem('userPhone', userPhone);
    localStorage.setItem('userEmail', userEmail);

    // Close modal
    document.getElementById('settings-modal').style.display = 'none';

    // Update email if it's already generated
    const emailBody = document.getElementById('email-body').innerHTML;
    if (emailBody && !emailBody.includes('[Your Name]')) {
        // Get company name from subject
        const subject = document.getElementById('email-subject').value;
        const companyMatch = subject.match(/Application for Position at (.+)/);
        const companyName = companyMatch ? companyMatch[1] : 'the Company';

        // Regenerate email with new settings
        const recruiterEmail = document.getElementById('email-to').value;
        generateEmail(companyName, recruiterEmail);
    }

    // Show confirmation
    alert('Settings saved successfully!');
}

// Event listeners for the new functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for the transform resume functionality
    document.getElementById('transform-resume-btn').addEventListener('click', transformResume);
    document.getElementById('download-resume-pdf').addEventListener('click', downloadResumePdf);
    document.getElementById('apply-to-email').addEventListener('click', applyToEmail);

    // Add event listener for the tab that ensures email is generated
    document.querySelector('[data-tab="email-template"]').addEventListener('click', ensureEmailGenerated);

    // Load user settings from localStorage
    loadUserSettings();

    // Add event listener for settings modal
    document.getElementById('settings-btn').addEventListener('click', function() {
        document.getElementById('settings-modal').style.display = 'block';
    });

    // Add event listener for closing the settings modal
    document.querySelector('.close').addEventListener('click', function() {
        document.getElementById('settings-modal').style.display = 'none';
    });

    // Add event listener for saving settings
    document.getElementById('save-settings').addEventListener('click', saveUserSettings);
});

// Function to extract company name and email from job description
function extractCompanyInfo(jobDescription) {
    let companyName = '';
    let recruiterEmail = '';

    // Common company identifiers in job descriptions
    const companyIdentifiers = [
        'at', 'with', 'for', 'join', 'company:', 'organization:', 'employer:',
        'about us', 'about the company', 'about the organization', 'about the employer',
        'company overview', 'organization overview', 'employer overview',
        'employer details'
    ];

    // Try to extract company name
    const lines = jobDescription.split('\n');

    // Special case for "Employer details" pattern
    const employerDetailsPattern = /employer\s*details\s*(.+)/i;
    for (const line of lines) {
        const match = line.match(employerDetailsPattern);
        if (match && match[1]) {
            companyName = match[1].trim();
            break;
        }
    }

    // Special case for "Posted on [date] by Employer details[Company]" pattern
    const postedByPattern = /posted\s+on.*by\s+employer\s+details\s*(.+)/i;
    for (const line of lines) {
        const match = line.match(postedByPattern);
        if (match && match[1]) {
            companyName = match[1].trim();
            break;
        }
    }

    // Handle the specific format from the example
    // Look for lines with "Employer details" followed by company name on the same or next line
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].toLowerCase().includes("employer details")) {
            // Check if company name is on the same line after "Employer details"
            const parts = lines[i].split(/employer\s+details/i);
            if (parts.length > 1 && parts[1].trim()) {
                companyName = parts[1].trim();
                break;
            }

            // If not, check the next line for the company name
            if (i + 1 < lines.length && lines[i + 1].trim()) {
                companyName = lines[i + 1].trim();
                break;
            }
        }
    }

    // Handle the specific example: "carpenter Posted on April 22, 2025 by Employer detailsApex Contractor & Developer Inc."
    const carpenterPattern = /Posted on .* by Employer details([A-Za-z0-9\s&]+)/i;
    const carpenterMatch = jobDescription.match(carpenterPattern);
    if (carpenterMatch && carpenterMatch[1]) {
        companyName = carpenterMatch[1].trim();
    }

    // Try one more pattern for the specific example
    if (!companyName) {
        // Look for "Employer details" without spaces before company name
        const noSpacePattern = /Employer details([A-Za-z0-9\s&]+)/i;
        const noSpaceMatch = jobDescription.match(noSpacePattern);
        if (noSpaceMatch && noSpaceMatch[1]) {
            companyName = noSpaceMatch[1].trim();
        }
    }

    // If no match found with the special pattern, try common patterns
    if (!companyName) {
        for (const line of lines) {
            const lowerLine = line.toLowerCase();

            // Check for lines that likely contain the company name
            for (const identifier of companyIdentifiers) {
                if (lowerLine.includes(identifier)) {
                    // Extract text after the identifier
                    const parts = line.split(new RegExp(`${identifier}\\s+`, 'i'));
                    if (parts.length > 1) {
                        // Extract up to the first punctuation or end of line
                        const potentialName = parts[1].split(/[,.;:]|\bis\b|\bwe\b/)[0].trim();
                        if (potentialName && potentialName.length > 2 && potentialName.length < 50) {
                            companyName = potentialName;
                            break;
                        }
                    }
                }
            }

            if (companyName) break;
        }
    }

    // If no company name found, look for capitalized words that might be a company name
    if (!companyName) {
        const capitalizedWordPattern = /\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b/g;
        const capitalizedMatches = jobDescription.match(capitalizedWordPattern);

        if (capitalizedMatches && capitalizedMatches.length > 0) {
            // Find the most frequent capitalized phrase that's likely a company name
            const wordCounts = {};
            for (const match of capitalizedMatches) {
                if (match.length > 3 && !['The', 'And', 'For', 'With', 'About'].includes(match)) {
                    wordCounts[match] = (wordCounts[match] || 0) + 1;
                }
            }

            // Find the most frequent capitalized phrase
            let maxCount = 0;
            for (const word in wordCounts) {
                if (wordCounts[word] > maxCount) {
                    maxCount = wordCounts[word];
                    companyName = word;
                }
            }
        }
    }

    // Extract email addresses
    const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    const emailMatches = jobDescription.match(emailPattern);

    if (emailMatches && emailMatches.length > 0) {
        // Use the first email found
        recruiterEmail = emailMatches[0];
    }

    // If still no company name, use a default
    if (!companyName) {
        companyName = "the Company";
    }

    // Log the extracted information for debugging
    console.log("Extracted company name:", companyName);
    console.log("Extracted email:", recruiterEmail);

    return { companyName, recruiterEmail };
}

// Function to generate email template
function generateEmail(companyName, recruiterEmail) {
    console.log("Generating email for company:", companyName);
    console.log("Recruiter email:", recruiterEmail);

    // Set email recipient
    document.getElementById('email-to').value = recruiterEmail || '';

    // Set email subject
    document.getElementById('email-subject').value = `Application for Position at ${companyName}`;

    // Extract keywords from job description to personalize email
    const jobDesc = document.getElementById('job-desc-input').value;
    const keywords = extractKeywords(jobDesc);

    // Get top skills to mention
    const topSkills = keywords.slice(0, 5).join(', ');
    console.log("Top skills:", topSkills);

    // Use Mathew's information for the signature
    const userName = 'Mathew Kasbarian';
    const userPhone = '6479639291';
    const userEmail = '<EMAIL>';
    console.log("User info:", userName, userPhone, userEmail);

    // Detect if this is a construction/trades job
    const isConstructionJob = isConstructionRelated(jobDesc);

    // Function to determine if a job is construction-related
    function isConstructionRelated(description) {
        const constructionTerms = [
            'construction', 'electrician', 'plumber', 'carpenter', 'welder', 'hvac',
            'concrete', 'masonry', 'drywall', 'framing', 'roofing', 'painting'
        ];

        const lowerDesc = description.toLowerCase();
        for (const term of constructionTerms) {
            if (lowerDesc.includes(term)) {
                return true;
            }
        }

        return false;
    }

    // Generate email body based on job type
    let emailBody = '';

    if (isConstructionJob) {
        // Construction/trades email template
        emailBody = `
            <p>Dear Hiring Manager at ${companyName},</p>

            <p>I am writing to apply for the construction position advertised at ${companyName}. With hands-on experience in ${topSkills.toLowerCase()}, I am confident in my ability to contribute effectively to your team.</p>

            <p>Throughout my career in construction, I have developed strong skills in safety procedures, efficient work practices, and quality workmanship. I understand the importance of reliability, punctuality, and teamwork on construction sites, and I take pride in completing projects to high standards.</p>

            <p>I have attached my resume (Mathew_Kasbarian_Resume.pdf) for your review, which outlines my relevant experience and qualifications. I am available to start immediately and can provide references upon request.</p>

            <p>Thank you for considering my application. I look forward to the opportunity to discuss how my skills and experience can benefit your team at ${companyName}.</p>

            <p>Sincerely,<br>
            ${userName}<br>
            ${userPhone}<br>
            ${userEmail}</p>
        `;
    } else {
        // Tech/office job email template
        emailBody = `
            <p>Dear Hiring Manager at ${companyName},</p>

            <p>I hope this email finds you well. I am writing to express my interest in the position advertised at ${companyName}. After reviewing the job description, I believe my skills and experience align well with what you're looking for.</p>

            <p>Based on your job posting, I understand you're looking for someone with expertise in ${topSkills}. My background and experience have prepared me well for this role, and I'm confident I can make valuable contributions to your team at ${companyName}.</p>

            <p>I have attached my resume (Mathew_Kasbarian_Resume.pdf) for your review, which details my relevant experience and qualifications. I would welcome the opportunity to discuss how my background, skills, and achievements can benefit ${companyName}.</p>

            <p>Thank you for considering my application. I look forward to the possibility of speaking with you soon about this exciting opportunity at ${companyName}.</p>

            <p>Best regards,<br>
            ${userName}<br>
            ${userPhone}<br>
            ${userEmail}</p>
        `;
    }

    console.log("Setting email body");
    document.getElementById('email-body').innerHTML = emailBody;
    console.log("Email body set");
}

// Function to open email in default mail client
function sendEmail() {
    const emailTo = document.getElementById('email-to').value;
    const emailSubject = document.getElementById('email-subject').value;
    const emailBody = document.getElementById('email-body').innerText;

    if (!emailTo) {
        alert('No recipient email address found. You can still copy the email content manually.');
        return;
    }

    // Create a mailto link
    const mailtoLink = `mailto:${encodeURIComponent(emailTo)}?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;

    // Open the link
    window.open(mailtoLink);

    // Show a reminder about attaching the resume
    setTimeout(() => {
        alert('Remember to attach your resume (Mathew_Kasbarian_Resume.pdf) to the email before sending!');
    }, 1000);
}