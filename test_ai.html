<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .result {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>AI Test Page</h1>
    <p>Use this page to test the AI functionality of the server.</p>
    
    <div>
        <button id="test-endpoint">Test AI Endpoint</button>
        <button id="test-resume">Test Resume Transformation</button>
    </div>
    
    <div class="result">
        <h2>Result:</h2>
        <pre id="result">No test run yet.</pre>
    </div>
    
    <script>
        document.getElementById('test-endpoint').addEventListener('click', async () => {
            try {
                const response = await fetch('http://localhost:8000/test-ai', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                
                const data = await response.json();
                document.getElementById('result').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('result').textContent = 'Error: ' + error.message;
            }
        });
        
        document.getElementById('test-resume').addEventListener('click', async () => {
            try {
                const resumeText = `
                    MATHEW KASBARIAN B.SC.
                    Business Systems Analyst with a B.Sc. in Computer Science and a Data Science minor.
                    Skilled in requirements gathering, user story creation, and system delivery in GxP-regulated environments.
                    Passionate about digital transformation, supply chain optimization, and automation.
                    
                    CONTACT
                    Markham, ON
                    ************
                    <EMAIL>
                    
                    EDUCATION
                    Ontario Tech University
                    Bachelor of Science (Hons)
                    Computer Science
                    Data Science (Minor)
                    
                    TECHNOLOGIES & TOOLS
                    Tools: JIRA, ALM/Retina, LucidSpark, Excel, Tableau, SharePoint
                    Languages: Python, SQL, Excel VBA, C#, JavaScript
                    Data Tools: PySpark, Matplotlib, Pandas, PostgreSQL
                    PLATFORMS: .NET, FIREBASE, AWS, QUICKBOOKS API
                    
                    EXPERIENCE
                    Software Developer
                    Toronto Automatic Doors Inc. – Dec 2024 – Present
                    - Wrote and tested C# (.NET) applications to automate invoicing and quoting, saving 1,500+ hours annually.
                    - Maintained internal web tools and automated form generation using HTML, CSS, and JavaScript.
                    - Authored technical documentation and user guides for operational and financial tools.
                    
                    Business Systems Analyst
                    Royal Bank of Canada – May 2022 – Dec 2024
                    - Gathered and documented business requirements, translating them into user stories and technical specifications.
                    - Collaborated with cross-functional teams to ensure successful system delivery in GxP-regulated environments.
                    - Developed and maintained data visualization dashboards using Tableau, providing actionable insights to stakeholders.
                    
                    Fitness Coach
                    All-In Athletics – Sep 2020 – May 2022
                    - Designed and implemented training programs for clients with diverse fitness goals and abilities.
                    - Utilized data-driven approaches to track progress and optimize training methodologies.
                    - Maintained detailed client records and progress reports using custom-built tracking software.
                `;
                
                const jobDescription = `
                    Software Developer at ABC Tech
                    
                    We are looking for a skilled Software Developer with experience in Python, JavaScript, and AWS.
                    The ideal candidate will have strong problem-solving skills and be able to work in a team environment.
                    
                    Responsibilities:
                    - Develop and maintain web applications
                    - Write clean, efficient code
                    - Collaborate with cross-functional teams
                    - Troubleshoot and debug applications
                    
                    Requirements:
                    - Bachelor's degree in Computer Science or related field
                    - 2+ years of experience in software development
                    - Proficiency in Python, JavaScript, and AWS
                    - Experience with web frameworks like React or Angular
                    - Strong problem-solving skills
                    
                    Employer details: ABC Tech Inc.
                `;
                
                document.getElementById('result').textContent = 'Sending request to server...';
                
                const response = await fetch('http://localhost:8000/transform-resume', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        resume: resumeText,
                        jobDescription: jobDescription
                    })
                });
                
                const data = await response.json();
                document.getElementById('result').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('result').textContent = 'Error: ' + error.message;
            }
        });
    </script>
</body>
</html>
