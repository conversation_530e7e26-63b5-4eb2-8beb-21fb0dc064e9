* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h1 {
    text-align: center;
    margin-bottom: 20px;
    color: #2c3e50;
}

h2 {
    margin-bottom: 15px;
    color: #3498db;
}

.tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #ddd;
}

.tab-btn {
    padding: 10px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    color: #7f8c8d;
    transition: all 0.3s ease;
}

.tab-btn.active {
    color: #3498db;
    border-bottom: 3px solid #3498db;
}

.tab-content {
    display: none;
    padding: 20px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tab-content.active {
    display: block;
}

textarea {
    width: 100%;
    height: 300px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    resize: vertical;
    font-family: inherit;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

input[type="text"],
input[type="email"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

button {
    padding: 10px 20px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #2980b9;
}

#analyze-btn {
    display: block;
    width: 100%;
    margin-top: 20px;
    padding: 15px;
    font-size: 18px;
    background-color: #2ecc71;
}

#analyze-btn:hover {
    background-color: #27ae60;
}

.actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

#resume-container {
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
    height: 500px;
}

#resume-frame {
    width: 100%;
    height: 100%;
    border: none;
}

#ai-suggestions {
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border-left: 4px solid #3498db;
    margin-bottom: 20px;
    display: none; /* Initially hidden, will be shown after analysis */
}

.email-preview {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.email-header {
    margin-bottom: 20px;
}

.email-body {
    min-height: 300px;
    padding: 15px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 20px;
}

#send-email {
    background-color: #9b59b6;
}

#send-email:hover {
    background-color: #8e44ad;
}

#download-pdf {
    background-color: #e74c3c;
}

#download-pdf:hover {
    background-color: #c0392b;
}

#save-resume {
    background-color: #f39c12;
}

#save-resume:hover {
    background-color: #d35400;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 80%;
    max-width: 500px;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #333;
}

/* Settings button */
.secondary-btn {
    background-color: #95a5a6;
    margin-left: 10px;
}

.secondary-btn:hover {
    background-color: #7f8c8d;
}

.email-actions {
    display: flex;
    margin-bottom: 20px;
}

.transform-resume-section {
    margin-top: 30px;
    padding: 20px;
    background-color: #f0f7ff;
    border-radius: 5px;
    border-left: 4px solid #3498db;
    text-align: center;
}

.transform-resume-section h3 {
    margin-top: 0;
    color: #2c3e50;
}

.transform-resume-section p {
    margin-bottom: 15px;
    color: #7f8c8d;
}

.primary-btn {
    background-color: #2ecc71;
    color: white;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: bold;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.primary-btn:hover {
    background-color: #27ae60;
}

/* Resume transformation styles */
.resume-transformation-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    margin: 20px auto;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.resume-improvements {
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border-left: 4px solid #3498db;
}

.resume-improvements h3 {
    margin-top: 0;
    color: #2c3e50;
}

.resume-improvements ul {
    padding-left: 20px;
}

.resume-improvements li {
    margin-bottom: 10px;
}

/* ATS Score Display */
.ats-score {
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f0f7ff;
    border-radius: 5px;
    border-left: 4px solid #2ecc71;
    text-align: center;
}

.ats-score h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 10px;
}

.score-bar {
    height: 20px;
    background-color: #ecf0f1;
    border-radius: 10px;
    overflow: hidden;
    margin-top: 10px;
}

.score-fill {
    height: 100%;
    background-color: #2ecc71;
    border-radius: 10px;
    transition: width 1s ease-in-out;
}

/* AI Model Badge */
.ai-model-badge {
    position: fixed;
    bottom: 10px;
    right: 10px;
    background-color: rgba(52, 152, 219, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    z-index: 1000;
}

.ai-model-info {
    margin-top: 10px;
    text-align: center;
    font-size: 14px;
    color: #7f8c8d;
    font-style: italic;
}

.resume-preview-container {
    margin-bottom: 30px;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
    background-color: white;
    min-height: 500px;
}

#resume-preview {
    font-family: 'Arial', sans-serif;
    line-height: 1.5;
}

.resume-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

/* Resume styling - updated to match Word doc format */
.resume-header {
    display: block;
    margin-bottom: 20px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 15px;
    position: relative;
    max-width: 100%;
}

.resume-name-section {
    display: flex;
    align-items: baseline;
    margin-bottom: 10px;
    width: 100%;
}

.resume-header h1 {
    margin: 0;
    padding: 0;
    color: #2c3e50;
    font-size: 24px;
    text-transform: uppercase;
    margin-right: 10px;
}

.resume-title {
    font-size: 16px;
    color: #2c3e50;
    font-weight: bold;
}

.resume-contact-info {
    text-align: right;
    position: absolute;
    top: 0;
    right: 0;
    border-left: 1px solid #ddd;
    padding-left: 20px;
    margin-left: 20px;
}

.resume-contact-info p {
    margin: 0;
    padding: 0;
    line-height: 1.5;
    font-size: 14px;
}

.resume-summary {
    flex: 1;
    margin-bottom: 20px;
    margin-top: 10px;
    max-width: 100%;
}

.resume-summary p {
    margin-top: 10px;
    line-height: 1.5;
    max-width: 100%;
    text-align: left;
    padding-right: 20px;
}

.summary-text {
    margin-right: 0;
    padding-right: 0;
    text-align: left;
    max-width: 100%;
    margin-left: 0;
    padding-left: 0;
    clear: both;
    display: block;
    width: 100%;
}

/* Professional description under name */
.professional-description {
    clear: both;
    display: block;
    width: 50%;
    max-width: 450px;
    margin-top: 40px;
    padding-top: 5px;
    line-height: 1.5;
    font-size: 14px;
    position: relative;
    box-sizing: border-box;
    padding-right: 0;
}

.resume-contact {
    flex: 1;
    text-align: right;
    position: absolute;
    right: 0;
    top: 0;
}

.contact-right {
    text-align: right;
}

.contact-left {
    text-align: left;
}

.resume-section {
    margin-bottom: 20px;
}

.resume-section h2 {
    color: #3498db;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
    margin-bottom: 15px;
    text-transform: uppercase;
    font-size: 18px;
}

.skills-grid {
    display: flex;
    justify-content: space-between;
}

.skills-column {
    flex: 1;
    padding-right: 10px;
}

.skills-column p {
    margin-bottom: 8px;
}

.tech-section p {
    margin-bottom: 12px;
}

.experience-item {
    margin-bottom: 20px;
}

.job-header {
    margin-bottom: 8px;
    display: block;
    width: 100%;
}

.job-title {
    font-weight: bold;
    margin-bottom: 2px;
    display: block;
    width: 100%;
}

.job-company, .job-date {
    font-style: italic;
    color: #7f8c8d;
    margin-bottom: 5px;
    display: block;
    width: 100%;
}

.job-duties {
    padding-left: 20px;
    margin-top: 5px;
}

.job-duties li {
    margin-bottom: 5px;
}

.education-item p {
    margin-bottom: 5px;
}

/* Make sure the resume fits on one page */
#resume-preview {
    font-family: 'Arial', sans-serif;
    line-height: 1.3;
    font-size: 14px;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* Ensure the resume is compact */
.resume-section h2 {
    margin-top: 0;
    margin-bottom: 10px;
}

.job-duties li {
    line-height: 1.3;
}

/* Chatbot Styles */
.chatbot-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #3498db;
    color: white;
    border: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.chatbot-toggle:hover {
    background-color: #2980b9;
    transform: scale(1.05);
}

.chat-icon {
    font-size: 24px;
}

.chatbot-container {
    position: fixed;
    bottom: 90px;
    right: 20px;
    width: 350px;
    height: 500px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 1000;
    display: none; /* Hidden by default */
}

.chatbot-header {
    background-color: #3498db;
    color: white;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chatbot-header h3 {
    margin: 0;
}

.close-chatbot {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.chatbot-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.user-message, .bot-message {
    max-width: 80%;
    padding: 10px 15px;
    border-radius: 18px;
    margin-bottom: 5px;
}

.user-message {
    align-self: flex-end;
    background-color: #3498db;
    color: white;
}

.bot-message {
    align-self: flex-start;
    background-color: #f1f1f1;
    color: #333;
}

.chatbot-input {
    display: flex;
    padding: 10px;
    border-top: 1px solid #ddd;
}

.chatbot-input input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 20px;
    margin-right: 10px;
}

.chatbot-input button {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 10px 15px;
    cursor: pointer;
}

.chatbot-input button:hover {
    background-color: #2980b9;
}
