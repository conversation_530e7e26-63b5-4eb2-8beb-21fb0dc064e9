%PDF-1.4
1 0 obj
<</Type /Catalog /Pages 2 0 R>>
endobj
2 0 obj
<</Type /Pages /Kids [3 0 R] /Count 1>>
endobj
3 0 obj
<</Type /Page /Parent 2 0 R /Resources 4 0 R /MediaBox [0 0 612 792] /Contents 6 0 R>>
endobj
4 0 obj
<</Font <</F1 5 0 R>>>>
endobj
5 0 obj
<</Type /Font /Subtype /Type1 /BaseFont /Helvetica>>
endobj
6 0 obj
<</Length 676>>
stream
BT
/F1 24 Tf
50 700 Td
(Math<PERSON> Kasbarian) Tj
/F1 12 Tf
0 -20 Td
(647-963-9291 | <EMAIL>) Tj
/F1 16 Tf
-50 -40 Td
(Professional Summary) Tj
/F1 12 Tf
0 -20 Td
(Developer with a B.Sc. in Computer Science and a Data Science minor. I specialize in automating) Tj
0 -15 Td
(workflows, building scalable software, and streamlining operations using Excel VBA, Python,) Tj
0 -15 Td
(C#, SQL, JavaScript, HTML and CSS. Passionate about saving organizations time through) Tj
0 -15 Td
(automation and empowering decision-makers with real-time, actionable data.) Tj
/F1 16 Tf
0 -30 Td
(Experience) Tj
/F1 14 Tf
0 -20 Td
(TORONTO AUTOMATIC DOORS INC. - Software Developer) Tj
/F1 12 Tf
0 -15 Td
(December 2024 - PRESENT) Tj
0 -15 Td
(- Automated creation of Estimates, POs, and Invoices in QuickBooks using C# (.NET)) Tj
0 -15 Td
(- Streamlined client onboarding by automating XML form generation) Tj
0 -15 Td
(- Enabled real-time KPI Dashboard and custom scripting using Excel VBA) Tj
ET
endstream
endobj
xref
0 7
0000000000 65535 f
0000000009 00000 n
0000000056 00000 n
0000000111 00000 n
0000000212 00000 n
0000000250 00000 n
0000000317 00000 n
trailer
<</Size 7/Root 1 0 R>>
startxref
1043
%%EOF
